import 'dart:typed_data';
import 'dart:ui';
import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:fpdart/fpdart.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;

import '../application/notification_bloc/notification_bloc.dart';
import '../domain/facade/notifications_facade.dart';
import '../domain/failure/notification_failure.dart';
import '../domain/model/notification_model.dart';

@LazySingleton(as: NotificationFacade)
class NotificationFacadeImpl implements NotificationFacade {
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin;
  final FirebaseMessaging _firebaseMessaging;

  // Stream behavior subject to handle notification events
  final StreamController<String?> _notificationStreamController =
      StreamController<String?>.broadcast();

  Stream<String?> get notificationStream =>
      _notificationStreamController.stream;

  // Global navigator key to show dialogs from anywhere
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  // To track if app was opened from notification
  bool appOpenedFromNotification = false;
  String? notificationPayload;
  //context
  BuildContext? getcontext;

  NotificationFacadeImpl(
    this._flutterLocalNotificationsPlugin,
    this._firebaseMessaging,
  );

  @override
  Future<Either<NotificationFailure, Unit>> initialize(
      BuildContext context) async {
    try {
      print(
          '------------------------------------------------------------------------------------------------------------');
      FirebaseMessaging.instance.getToken().then((value) {
        String? token = value;
        print('Token: $token');
      });
      getcontext = context;

      //ask for permission android

      await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
      );

      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@drawable/notification_icon');

      DarwinInitializationSettings initializationSettingsIOS =
          const DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      InitializationSettings initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Add the notification response handler
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onDidReceiveNotificationResponse,
      );

      // Check for initial notification that may have opened the app
      final NotificationAppLaunchDetails? launchDetails =
          await _flutterLocalNotificationsPlugin
              .getNotificationAppLaunchDetails();

      if (launchDetails != null && launchDetails.didNotificationLaunchApp) {
        appOpenedFromNotification = true;
        notificationPayload = launchDetails.notificationResponse?.payload;
        print('App opened from notification: $notificationPayload');
        _notificationStreamController.add(notificationPayload);
      }

      await _firebaseMessaging.requestPermission();

      Future<Uint8List> _getByteArrayFromUrl(String url) async {
        final http.Response response = await http.get(Uri.parse(url));
        return response.bodyBytes;
      }

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
        RemoteNotification? notification = message.notification;
        AndroidNotification? android = message.notification?.android;
        AppleNotification? apple = message.notification?.apple;
        final ByteArrayAndroidBitmap largeIcon = ByteArrayAndroidBitmap(
            await _getByteArrayFromUrl('https://dummyimage.com/48x48'));

        if (notification != null) {
          // App is in foreground - show dialog directly
          _showNotificationDialog(
            getcontext!, // Use the stored context
            notification.title ?? 'Notification',
            notification.body ?? '',
            message.data.toString(),
          );

          await _saveNotification(NotificationModel(
            id: notification.hashCode.toInt(),
            title: notification.title!,
            body: notification.body!,
            receivedAt: tz.TZDateTime.now(tz.local),
            payload: message.data.toString(),
          ));
        }
      });

      // Handle background/terminated message taps via Firebase
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        print('Message opened app: ${message.notification?.title}');
        if (message.notification != null) {
          _showNotificationDialog(
            context,
            message.notification?.title ?? 'Notification',
            message.notification?.body ?? '',
            message.data.toString(),
          );
        }
      });

      return const Right(unit);
    } catch (e) {
      print('Error in notification initialization: $e');
      return Left(NotificationFailure.getNotificationFailure(e.toString()));
    }
  }

  // Enhanced notification response handler to detect therapy feedback notifications
  void _onDidReceiveNotificationResponse(NotificationResponse response) {
    print('Notification response received: ${response.payload}');
    appOpenedFromNotification = true;
    notificationPayload = response.payload;

    _notificationStreamController.add(response.payload);

    // Add the payload to the stream

    // Handle the notification payload
    if (response.payload != null) {
      try {
        final notificationData = jsonDecode(response.payload!);
        final title = notificationData['title'] ?? 'Notification';
        final body = notificationData['body'] ?? '';
        final originalPayload = notificationData['originalPayload'];

        // Check if this is a therapy feedback notification by notification type
        final notificationType = notificationData['notificationType'];
        if (notificationType == 'therapy_feedback') {
          // This is a therapy feedback notification - don't show dialog here
          // Let the app layer handle it through the notification stream
          print(
              'Therapy feedback notification received, letting app handle it: $originalPayload');

          // The notification will be handled by the app layer through the existing stream
          return;
        } else {
          // Show regular notification dialog for non-therapy notifications
          notificationPayload = response.payload;
          print(
              'Notification Payload: $notificationPayload===================================');
          _showNotificationDialog(
            getcontext!,
            title,
            body,
            response.payload,
          );
        }
      } catch (e) {
        print('Error parsing notification payload: $e');
      }
    }
  }

  Future<void> _saveTherapyId(String therapyId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('last_therapy_id', therapyId);
  }

  // Show dialog for foreground notifications with enhanced check for therapy notifications
  void _showNotificationDialog(
      BuildContext context, String title, String body, String? payload) {
    // Check if this is a therapy feedback notification
    if (payload != null) {
      try {
        // Show therapy feedback dialog instead
        // _showTherapyFeedbackDialog(context);
        return;
      } catch (e) {
        print('Error parsing payload in showNotificationDialog: $e');
      }
    }

// Example implementation for getting app context
// You should implement this based on your app's architecture
    BuildContext getAppContext() {
      // Use a Navigator key approach or Provider pattern
      // This is a placeholder
      return navigatorKey.currentContext!;
    }

    // Regular notification dialog for non-therapy notifications
    print(context);
    showDialog(
      context: getcontext!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(body),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _saveNotification(NotificationModel notification) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> notifications =
        prefs.getStringList('recent_notifications') ?? [];

    // Convert notification to JSON string and add to the front
    notifications.insert(0, jsonEncode(notification.toJson()));

    // Keep only the 20 most recent notifications
    if (notifications.length > 20) {
      notifications = notifications.sublist(0, 20);
    }

    await prefs.setStringList('recent_notifications', notifications);
  }

  Future<List<NotificationModel>> _getRecentNotifications() async {
    final prefs = await SharedPreferences.getInstance();
    List<String> notifications =
        prefs.getStringList('recent_notifications') ?? [];

    return notifications
        .map((e) => NotificationModel.fromJson(jsonDecode(e)))
        .toList();
  }

  @override
  Future<Either<NotificationFailure, Unit>> scheduleNotification(
      NotificationModel notification, String frequency,
      {Widget? widget}) async {
    try {
      print(
          '#####################################################${notification.payload}');
      tz.initializeTimeZones();
      final tz.TZDateTime scheduledTime =
          tz.TZDateTime.now(tz.local).add(const Duration(seconds: 5));
      DateTimeComponents? matchComponents;
      print('Scheduled Time: $scheduledTime');

      // Determine matchDateTimeComponents based on frequency
      switch (frequency.toLowerCase()) {
        case 'weekly':
          matchComponents = DateTimeComponents.dayOfWeekAndTime;
          break;
        case 'daily':
          matchComponents = DateTimeComponents.time;
          break;
        case 'monthly':
          matchComponents = DateTimeComponents.dayOfMonthAndTime;
          break;
        case 'single':
          // For single notifications, don't use matchDateTimeComponents
          matchComponents = null;
          break;
        default:
          return Left(
              NotificationFailure.getNotificationFailure('Invalid frequency'));
      }

      try {
        // For single notifications, ensure scheduledTime is in the future
        if (frequency.toLowerCase() == 'single' &&
            scheduledTime.isBefore(tz.TZDateTime.now(tz.local))) {
          print('Scheduled time is in the past');
          return Left(NotificationFailure.getNotificationFailure(
              'Scheduled time is in the past'));
        }

        // Print notification details for debugging
        print(
            'Scheduling notification: ID=${notification.id}, Title=${notification.title}');
        print('Body=${notification.body}, Time=$scheduledTime');
        print('Frequency=$frequency, MatchComponents=$matchComponents');

        // Save notification payload with all required information
        final notificationPayload = jsonEncode({
          'id': notification.id,
          'title': notification.title,
          'body': notification.body,
          'originalPayload': notification.payload,
        });

        await _flutterLocalNotificationsPlugin.zonedSchedule(
          notification.id!,
          notification.title ?? "Notification Title",
          notification.body ?? "Notification Body",
          tz.TZDateTime.now(tz.local).add(const Duration(seconds: 10)),
          const NotificationDetails(
            android: AndroidNotificationDetails(
              'high_importance_channel', // Use a consistent channel ID
              'High Importance Notifications',
              channelDescription: 'Channel for important notifications',
              importance: Importance.max,
              priority: Priority.high,
              showWhen: true,
              autoCancel: false,
              // Use a known icon from your assets
              icon: '@drawable/notification_icon',
              // Ensure notification opens app when tapped
              fullScreenIntent: true,
            ),
            iOS: DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: true,
              interruptionLevel: InterruptionLevel.timeSensitive,
            ),
          ),
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          payload: notificationPayload,
          matchDateTimeComponents: matchComponents,
        );

        final pendingNotifications = await _flutterLocalNotificationsPlugin
            .pendingNotificationRequests();
        print('🔍 Pending notifications count: ${pendingNotifications.length}');
        for (var notification in pendingNotifications) {
          print(
              '📌 Pending - ID: ${notification.id}, Title: ${notification.title}, Body: ${notification}');
        }
        print('Notification scheduled successfully for $scheduledTime');
        return const Right(unit);
      } catch (e) {
        print('Error scheduling notification: $e');
        return Left(NotificationFailure.getNotificationFailure(e.toString()));
      }
    } catch (e) {
      print('Error in notification scheduling: $e');
      return Left(NotificationFailure.getNotificationFailure(e.toString()));
    }
  }

  @override
  Future<Either<NotificationFailure, Unit>> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
      return const Right(unit);
    } catch (e) {
      return Left(NotificationFailure.getNotificationFailure(e.toString()));
    }
  }

  Future<Either<NotificationFailure, List<NotificationModel>>>
      getRecentNotifications() async {
    try {
      final notifications = await _getRecentNotifications();
      return Right(notifications);
    } catch (e) {
      return Left(NotificationFailure.getNotificationFailure(e.toString()));
    }
  }

  @override
  Future<Either<NotificationFailure, Unit>> deleteAllNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('recent_notifications');
      return const Right(unit);
    } catch (e) {
      return Left(NotificationFailure.deleteNotificationFailure(e.toString()));
    }
  }

  @override
  Future<Either<NotificationFailure, Unit>> deleteNotification(int id) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      List<String> notifications =
          prefs.getStringList('recent_notifications') ?? [];
      notifications
          .removeWhere((element) => jsonDecode(element)['id'] == id.toString());
      await prefs.setStringList('recent_notifications', notifications);
      return const Right(unit);
    } catch (e) {
      return Left(NotificationFailure.deleteNotificationFailure(e.toString()));
    }
  }

  @override
  Future<Either<NotificationFailure, List<NotificationModel>>>
      getNotifications() async {
    try {
      final notifications = await _getRecentNotifications();
      return Right(notifications);
    } catch (e) {
      return Left(NotificationFailure.getNotificationFailure(e.toString()));
    }
  }

  // Method to check if the app was opened from a notification and show dialog if needed
  @override
  Future<Either<NotificationFailure, Unit>> checkForNotifications(
      BuildContext context) async {
    try {
      if (appOpenedFromNotification) {
        appOpenedFromNotification = false;

        if (notificationPayload != null) {
          final notificationData = jsonDecode(notificationPayload!);
          final title = notificationData['title'] ?? 'Notification';
          final body = notificationData['body'] ?? '';
          final originalPayload = notificationData['originalPayload'];

          // Check if this is a therapy feedback notification by notification type
          final notificationType = notificationData['notificationType'];
          if (notificationType == 'therapy_feedback') {
            // This is a therapy feedback notification - don't show dialog here
            // Let the app layer handle it through the notification stream
            print(
                'Therapy feedback notification in checkForNotifications, letting app handle it');
          } else {
            // Regular notification
            _showNotificationDialog(
              context,
              title,
              body,
              notificationPayload,
            );
          }
        }
      }
      return const Right(unit);
    } catch (e) {
      return Left(NotificationFailure.getNotificationFailure(e.toString()));
    }
  }

  // Dispose the stream controller when no longer needed
  void dispose() {
    _notificationStreamController.close();
  }

  // Method to schedule a notification using WorkManager
  @override
  Future<Either<NotificationFailure, Unit>> scheduleWorkManagerNotification(
      NotificationModel notificationModel, String s) async {
    try {
      // WorkManager scheduling logic to trigger stream
      // Workmanager().registerOneOffTask(
      //   taskId: notificationModel.id.toString(),
      //   task: () {
      //     // Trigger the notification stream
      //     _notificationStreamController.add(notificationModel.payload);
      //   },
      //   frequency: s,
      // );
      return const Right(unit);
    } catch (e) {
      return Left(NotificationFailure.getNotificationFailure(e.toString()));
    }
  }

  @override
  Stream<Either<NotificationFailure, NotificationModel>>
      streamNotifications() async* {
    // Stream logic to emit notifications
    yield* _notificationStreamController.stream.map((payload) {
      print('🔍 Stream received payload: $payload');
      if (payload != null) {
        try {
          final notificationData = jsonDecode(payload);
          print('🔍 Parsed notification data: $notificationData');

          // Create NotificationModel from notification response data
          final notificationModel = NotificationModel(
            id: notificationData['id'],
            title: notificationData['title'],
            body: notificationData['body'],
            payload: notificationData[
                'originalPayload'], // Map originalPayload to payload
            notificationType:
                'therapy_feedback', // Set the notification type for therapy feedback
          );

          print(
              '✅ Created NotificationModel: id=${notificationModel.id}, type=${notificationModel.notificationType}, payload=${notificationModel.payload}');
          return Right(notificationModel);
        } catch (e) {
          print('❌ Error creating NotificationModel from payload: $e');
          return Left(NotificationFailure.getNotificationFailure(e.toString()));
        }
      } else {
        print('❌ No payload received in stream');
        return Left(NotificationFailure.getNotificationFailure('No payload'));
      }
    });
  }
}
