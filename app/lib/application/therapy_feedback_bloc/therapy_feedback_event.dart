part of 'therapy_feedback_bloc.dart';

abstract class TherapyFeedbackEvent {
  const TherapyFeedbackEvent();
}

class LoadSessionData extends TherapyFeedbackEvent {
  final String sessionId;
  const LoadSessionData(this.sessionId);
}

class SubmitFeedback extends TherapyFeedbackEvent {
  final String sessionId;
  final String? feedbackText;
  final int? painLevelBefore;
  final int? painLevelAfter;
  final int? satisfactionLevel;

  const SubmitFeedback({
    required this.sessionId,
    this.feedbackText,
    this.painLevelBefore,
    this.painLevelAfter,
    this.satisfactionLevel,
  });
}

class ResetState extends TherapyFeedbackEvent {
  const ResetState();
}
