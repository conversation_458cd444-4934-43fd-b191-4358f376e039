part of 'therapy_feedback_bloc.dart';

abstract class TherapyFeedbackState {
  const TherapyFeedbackState();
}

class InitialState extends TherapyFeedbackState {
  const InitialState();
}

class LoadingState extends TherapyFeedbackState {
  const LoadingState();
}

class SessionLoadedState extends TherapyFeedbackState {
  final TherapySessionModel session;
  const SessionLoadedState(this.session);
}

class SubmittingState extends TherapyFeedbackState {
  const SubmittingState();
}

class SubmittedState extends TherapyFeedbackState {
  const SubmittedState();
}

class ErrorState extends TherapyFeedbackState {
  final String message;
  const ErrorState(this.message);
}
