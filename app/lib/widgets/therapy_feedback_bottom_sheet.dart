import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:analytics/domain/models/therapy_session_model.dart';
import 'package:analytics/analytics.dart';
import 'package:get_it/get_it.dart';

class TherapyFeedbackBottomSheet extends StatefulWidget {
  final String sessionId;

  const TherapyFeedbackBottomSheet({
    Key? key,
    required this.sessionId,
  }) : super(key: key);

  @override
  State<TherapyFeedbackBottomSheet> createState() =>
      _TherapyFeedbackBottomSheetState();

  static void show(BuildContext context, String sessionId) {
    showModalBottomSheet<void>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => BlocProvider(
        create: (context) => GetIt.instance<TherapyFeedbackBloc>()
          ..add(TherapyFeedbackEvent.loadSessionData(sessionId)),
        child: TherapyFeedbackBottomSheet(sessionId: sessionId),
      ),
    );
  }
}

class _TherapyFeedbackBottomSheetState
    extends State<TherapyFeedbackBottomSheet> {
  final TextEditingController _feedbackController = TextEditingController();
  int _painLevelBefore = 5;
  int _painLevelAfter = 5;
  int _satisfactionLevel = 3;

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) {
          return BlocConsumer<TherapyFeedbackBloc, TherapyFeedbackState>(
            listener: (context, state) {
              state.when(
                initial: () {},
                loading: () {},
                sessionLoaded: (session) {},
                submitting: () {},
                submitted: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Thank you for your feedback!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                error: (message) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(message),
                      backgroundColor: Colors.red,
                    ),
                  );
                },
              );
            },
            builder: (context, state) {
              return SingleChildScrollView(
                controller: scrollController,
                padding: EdgeInsets.all(20.r),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Handle bar
                    Center(
                      child: Container(
                        width: 40.w,
                        height: 4.h,
                        decoration: BoxDecoration(
                          color: Colors.grey[300],
                          borderRadius: BorderRadius.circular(2.r),
                        ),
                      ),
                    ),
                    SizedBox(height: 20.h),

                    // Title
                    Text(
                      'How was your therapy session?',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 20.h),

                    // Session details section
                    state.when(
                      initial: () => const SizedBox(),
                      loading: () =>
                          const Center(child: CircularProgressIndicator()),
                      sessionLoaded: (session) => _buildSessionDetails(session),
                      submitting: () =>
                          const Center(child: CircularProgressIndicator()),
                      submitted: () => const SizedBox(),
                      error: (message) => const SizedBox(),
                    ),

                    SizedBox(height: 20.h),

                    // Pain level before
                    _buildSectionTitle('Pain level before therapy'),
                    _buildPainSlider(
                      value: _painLevelBefore,
                      onChanged: (value) =>
                          setState(() => _painLevelBefore = value),
                    ),
                    SizedBox(height: 20.h),

                    // Pain level after
                    _buildSectionTitle('Pain level after therapy'),
                    _buildPainSlider(
                      value: _painLevelAfter,
                      onChanged: (value) =>
                          setState(() => _painLevelAfter = value),
                    ),
                    SizedBox(height: 20.h),

                    // Satisfaction level
                    _buildSectionTitle(
                        'How satisfied are you with this session?'),
                    _buildSatisfactionSlider(),
                    SizedBox(height: 20.h),

                    // Feedback text
                    _buildSectionTitle('Additional feedback (optional)'),
                    SizedBox(height: 10.h),
                    TextField(
                      controller: _feedbackController,
                      maxLines: 4,
                      decoration: InputDecoration(
                        hintText:
                            'Share your thoughts about this therapy session...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        contentPadding: EdgeInsets.all(16.r),
                      ),
                    ),
                    SizedBox(height: 30.h),

                    // Submit button
                    SizedBox(
                      width: double.infinity,
                      height: 50.h,
                      child: ElevatedButton(
                        onPressed: state.maybeWhen(
                          submitting: () => null,
                          orElse: () => _submitFeedback,
                        ),
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                        ),
                        child: state.when(
                          submitting: () => const CircularProgressIndicator(
                              color: Colors.white),
                          initial: () => Text('Submit Feedback',
                              style: TextStyle(fontSize: 16.sp)),
                          loading: () => Text('Submit Feedback',
                              style: TextStyle(fontSize: 16.sp)),
                          sessionLoaded: (session) => Text('Submit Feedback',
                              style: TextStyle(fontSize: 16.sp)),
                          submitted: () => Text('Submit Feedback',
                              style: TextStyle(fontSize: 16.sp)),
                          error: (message) => Text('Submit Feedback',
                              style: TextStyle(fontSize: 16.sp)),
                        ),
                      ),
                    ),
                    SizedBox(height: 10.h),

                    // Skip button
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: Text(
                          'Skip for now',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                    ),

                    // Add bottom padding for keyboard
                    SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildSessionDetails(TherapySessionModel session) {
    final startTime = session.sessionInfo.therapyStartTime;
    final endTime = session.sessionInfo.therapyEndTime;
    final duration = endTime != null && startTime != null
        ? endTime.difference(startTime)
        : null;

    return Container(
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Session Details',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: Colors.blue[800],
            ),
          ),
          SizedBox(height: 12.h),
          _buildDetailRow('Date',
              DateFormat('MMM dd, yyyy').format(startTime ?? DateTime.now())),
          _buildDetailRow('Start Time',
              DateFormat('h:mm a').format(startTime ?? DateTime.now())),
          if (endTime != null)
            _buildDetailRow('End Time', DateFormat('h:mm a').format(endTime)),
          if (duration != null)
            _buildDetailRow('Duration', '${duration.inMinutes} minutes'),
          _buildDetailRow(
              'Heat Level', '${session.mostUsedSettings.heatLevel}'),
          _buildDetailRow(
              'TENS Level', '${session.mostUsedSettings.tensLevel}'),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildPainSlider({
    required int value,
    required void Function(int) onChanged,
  }) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'No Pain',
              style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
            ),
            Text(
              'Worst Pain',
              style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
            ),
          ],
        ),
        SizedBox(height: 5.h),
        Slider(
          value: value.toDouble(),
          min: 0,
          max: 10,
          divisions: 10,
          label: value.toString(),
          onChanged: (newValue) => onChanged(newValue.toInt()),
        ),
        Text(
          '$value/10',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: _getPainColor(value),
          ),
        ),
      ],
    );
  }

  Widget _buildSatisfactionSlider() {
    const satisfactionLabels = [
      'Very Poor',
      'Poor',
      'Okay',
      'Good',
      'Excellent'
    ];

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Very Poor',
              style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
            ),
            Text(
              'Excellent',
              style: TextStyle(fontSize: 12.sp, color: Colors.grey[600]),
            ),
          ],
        ),
        SizedBox(height: 5.h),
        Slider(
          value: _satisfactionLevel.toDouble(),
          min: 1,
          max: 5,
          divisions: 4,
          label: satisfactionLabels[_satisfactionLevel - 1],
          onChanged: (value) =>
              setState(() => _satisfactionLevel = value.toInt()),
        ),
        Text(
          satisfactionLabels[_satisfactionLevel - 1],
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: _getSatisfactionColor(_satisfactionLevel),
          ),
        ),
      ],
    );
  }

  Color _getPainColor(int painLevel) {
    if (painLevel <= 3) return Colors.green;
    if (painLevel <= 6) return Colors.orange;
    return Colors.red;
  }

  Color _getSatisfactionColor(int satisfaction) {
    if (satisfaction <= 2) return Colors.red;
    if (satisfaction <= 3) return Colors.orange;
    return Colors.green;
  }

  void _submitFeedback() {
    context.read<TherapyFeedbackBloc>().add(
          TherapyFeedbackEvent.submitFeedback(
            sessionId: widget.sessionId,
            feedbackText: _feedbackController.text.trim().isEmpty
                ? null
                : _feedbackController.text.trim(),
            painLevelBefore: _painLevelBefore,
            painLevelAfter: _painLevelAfter,
            satisfactionLevel: _satisfactionLevel,
          ),
        );
  }
}
