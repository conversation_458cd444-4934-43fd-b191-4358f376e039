import 'dart:async';
import 'package:account_management/application/menstrual_cycle_bloc/menstrual_cycle_bloc.dart';
import 'package:account_management/application/onboardin_form_bloc/onboarding_form_bloc.dart';
import 'package:account_management/account_management.dart';
import 'package:account_management/domain/facade/period_tracking_facade.dart';
import 'package:analytics/analytics.dart' hide getIt;
import 'package:authentication/application/bloc/email_verification/email_verification_bloc.dart';
import 'package:auto_route/auto_route.dart';
import 'package:bluetooth/application/bluetooth_service_bloc/bluetooth_service_bloc.dart';
import 'package:design_system/design_system.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:juno_plus/routing/app_pages.dart';
import 'package:juno_plus/routing/app_pages.gr.dart';
import 'package:authentication/authentication.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:notifications/application/manage_scheduled_notifications_bloc/manage_scheduled_notifications_bloc.dart';
import 'services/app_lifecycle_observer.dart';
import 'services/therapy_feedback_service.dart';
import 'helpers.dart';

class JunoPlus extends StatefulWidget {
  final String env;

  JunoPlus({
    required this.env,
    Key? key,
  }) : super(key: key);

  @override
  State<JunoPlus> createState() => _JunoPlusState();
}

class _JunoPlusState extends State<JunoPlus> {
  final _appRouter = AppRouter();
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();
  AppLifecycleObserver? _lifecycleObserver;

  @override
  void initState() {
    super.initState();
    _initializeLifecycleObserver();
  }

  void _initializeLifecycleObserver() {
    _lifecycleObserver = AppLifecycleObserver(_handleAppLifecycleChange);
    _lifecycleObserver!.initialize();
  }

  void _handleAppLifecycleChange(AppLifecycle lifecycle) {
    if (lifecycle == AppLifecycle.resumed) {
      // App came to foreground - check for pending feedback
      _checkPendingFeedback();
    }
  }

  Future<void> _checkPendingFeedback() async {
    try {
      final therapyFeedbackService = getIt<TherapyFeedbackService>();
      await therapyFeedbackService.handlePendingFeedback();
    } catch (e) {
      print('❌ Error checking pending feedback: $e');
    }
  }

  @override
  void dispose() {
    _lifecycleObserver?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(750, 1334),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) =>
                  getIt<AuthBloc>()..add(const AuthEvent.authCheckRequested()),
            ),
            BlocProvider(
                create: (_) => getIt<ManageScheduledNotificationsBloc>()),
            BlocProvider(
              create: (_) => getIt<BluetoothServiceBloc>()
                ..add(const BluetoothServiceEvent.checkBluetooth()),
            ),
            BlocProvider<OnboardingFormBloc>(
              create: (context) => getIt<OnboardingFormBloc>(),
            ),
            BlocProvider<SignInBloc>(
              create: (context) => getIt<SignInBloc>(),
            ),
            BlocProvider<EmailVerificationBloc>(
              create: (context) => getIt<EmailVerificationBloc>(),
            ),
            BlocProvider<MenstrualCycleBloc>(
              create: (context) => getIt<MenstrualCycleBloc>()
                // ..add(MenstrualCycleEvent.getInitialData())
                ..add(MenstrualCycleEvent.watchAllStarted()),
            ),
            // Add AnalyticsInitializationBloc to handle analytics startup
            BlocProvider<AnalyticsInitializationBloc>(
              create: (context) => getIt<AnalyticsInitializationBloc>()
                ..add(InitializeAnalyticsEvent()),
            ),
            // Initialize period reminders on app startup
            BlocProvider<PeriodReminderSettingsBloc>(
              create: (context) {
                final bloc = getIt<PeriodReminderSettingsBloc>();
                // Initialize period reminders system
                getIt<PeriodTrackingFacade>().initializePeriodReminders();
                return bloc
                  ..add(const PeriodReminderSettingsEvent.loadSettings());
              },
            ),
          ],
          child: MaterialApp.router(
            routerConfig: _appRouter.config(),
            key: _navigatorKey, // Use our navigator key
            title: 'Juno Plus',
            theme: AppTheme.lightTheme,
            debugShowCheckedModeBanner: false,
          ),
        );
      },
    );
  }
}

@RoutePage()
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  bool isUserAuthenticated = false;
  bool hasNavigated = false;
  StreamSubscription<AuthState>? _authSubscription;

  @override
  void initState() {
    super.initState();
    navigateUser();
  }

  Future<void> navigateUser() async {
    final authBloc = BlocProvider.of<AuthBloc>(context);
    _authSubscription = authBloc.stream.listen((state) {
      if (hasNavigated) return;

      state.maybeMap(
        authenticated: (state) {
          // Navigate to HomePage
          if (state.user.isEmailVerified!) {
            if (state.user.isOnboarded!) {
              context.router.popAndPush(HomeRoute());
            } else {
              context.router.popAndPush(GetStartedRoute());
            }
          } else {
            context.router.popAndPush(EmailVerificationRoute());
          }
          hasNavigated = true;
        },
        unauthenticated: (_) {
          // Navigate to WelcomePage
          context.router.popAndPush(WelcomeRoute());
          hasNavigated = true;
        },
        orElse: () {},
      );
    });
    authBloc.add(AuthCheckRequested());
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: CircularProgressIndicator(),
    );
  }
}
