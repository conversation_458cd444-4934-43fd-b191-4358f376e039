import 'package:authentication/di/di.dart' as authentication_di;
import 'package:bluetooth/di/di.dart' as bluetooth_di;
import 'package:notifications/di/di.dart' as notifications_di;
import 'package:account_management/di/di.dart' as account_management_di;
import 'package:help_center/di/di.dart' as help_center_di;
import 'package:remote/di/di.dart' as remote_di;
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart';
import 'package:analytics/domain/facade/analytics_facade.dart';
import 'package:injectable/injectable.dart';
import 'package:get_it/get_it.dart';
import 'package:analytics/di/di.dart' as analytics_di;
import 'package:flutter/material.dart';

import 'di.config.dart';
import 'helpers.dart';
import 'services/therapy_feedback_service.dart';

@module
abstract class ExternalDependencyModule {
  @Named("externalNotificationsFacade")
  @injectable
  ScheduledNotificationsFacade get notificationsFacade =>
      getIt<ScheduledNotificationsFacade>();
}

@InjectableInit()
void configureDependencies({
  required String env,
  bool isTest = false,
}) {
  getIt.init(environment: env);
  if (isTest) {
    getIt.allowReassignment = true;
  }
}

Future<void> configureAllPackagesDependencies({
  required String envId,
  bool isTest = false,
}) async {
  getIt.allowReassignment = isTest;

  try {
    // 1. Configure notifications first since other packages depend on it
    notifications_di.configureDependencies(env: envId, isTest: isTest);

    // 2. Register notifications facade for other modules (handled by generated DI config)

    // 3. Configure Bluetooth first since RemoteControlFacade depends on it
    bluetooth_di.configureDependencies(env: envId, isTest: isTest);

    // 4. Configure remote next since analytics and account management depend on it
    remote_di.configureDependencies(env: envId, isTest: isTest);

    // 5. Configure analytics since account management depends on TelemetryFacade
    analytics_di.configureDependencies(env: envId, isTest: isTest);

    // 6. Configure app-specific dependencies
    configureDependencies(env: envId, isTest: isTest);

    // 7. Register TherapyFeedbackService manually
    if (!getIt.isRegistered<TherapyFeedbackService>()) {
      getIt.registerLazySingleton<TherapyFeedbackService>(
        () => TherapyFeedbackService(
          getIt<ScheduledNotificationsFacade>(),
          getIt<IAnalyticsFacade>(),
          getIt<GlobalKey<NavigatorState>>(),
        ),
      );
    }

    // 8. Configure remaining packages that have less critical dependencies
    account_management_di.configureDependencies(env: envId, isTest: isTest);
    help_center_di.configureDependencies(env: envId, isTest: isTest);
    authentication_di.configureDependencies(env: envId, isTest: isTest);
  } catch (e) {
    if (e.toString().contains('already registered') && !isTest) {
      // In production, we want to know about registration conflicts
      rethrow;
    }
    // In test mode, we allow re-registration
    print('Warning: Dependency registration conflict in test mode: $e');
  }
}
