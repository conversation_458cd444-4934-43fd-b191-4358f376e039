import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:analytics/domain/facade/analytics_facade.dart';
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart';
import '../services/therapy_feedback_service.dart';
import '../widgets/therapy_feedback_bottom_sheet.dart';

/// Demo page to test the therapy feedback feature
class TherapyFeedbackDemoPage extends StatefulWidget {
  const TherapyFeedbackDemoPage({Key? key}) : super(key: key);

  @override
  State<TherapyFeedbackDemoPage> createState() =>
      _TherapyFeedbackDemoPageState();
}

class _TherapyFeedbackDemoPageState extends State<TherapyFeedbackDemoPage> {
  late TherapyFeedbackService _feedbackService;
  String? _currentSessionId;

  @override
  void initState() {
    super.initState();
    _feedbackService = TherapyFeedbackService(
      GetIt.instance<ScheduledNotificationsFacade>(),
      GetIt.instance<IAnalyticsFacade>(),
      GetIt.instance<GlobalKey<NavigatorState>>(),
    );
    _getCurrentSession();
  }

  Future<void> _getCurrentSession() async {
    final sessionId =
        await GetIt.instance<IAnalyticsFacade>().getCurrentSessionId();
    setState(() {
      _currentSessionId =
          sessionId ?? 'demo_session_${DateTime.now().millisecondsSinceEpoch}';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Therapy Feedback Demo'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Current Session',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('Session ID: ${_currentSessionId ?? 'Loading...'}'),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // Test buttons
            ElevatedButton(
              onPressed:
                  _currentSessionId != null ? _showFeedbackBottomSheet : null,
              child: const Text('Show Feedback Bottom Sheet'),
            ),
            const SizedBox(height: 10),

            ElevatedButton(
              onPressed: _currentSessionId != null
                  ? _scheduleFeedbackNotification
                  : null,
              child: const Text('Schedule Feedback Notification (5 sec)'),
            ),
            const SizedBox(height: 10),

            ElevatedButton(
              onPressed: _currentSessionId != null ? _simulateSessionEnd : null,
              child: const Text('Simulate Session End'),
            ),
            const SizedBox(height: 10),

            ElevatedButton(
              onPressed: _checkPendingFeedback,
              child: const Text('Check Pending Feedback'),
            ),

            const SizedBox(height: 30),

            Card(
              color: Colors.amber[50],
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.amber[800]),
                        const SizedBox(width: 8),
                        Text(
                          'How to Test',
                          style:
                              Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.amber[800],
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '1. Tap "Show Feedback Bottom Sheet" to see the feedback UI\n'
                      '2. Tap "Schedule Feedback Notification" to test notifications\n'
                      '3. Tap "Simulate Session End" to test the full flow\n'
                      '4. When you get a notification, tap it to open feedback\n'
                      '5. The feedback will be saved to the session data',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showFeedbackBottomSheet() {
    if (_currentSessionId != null) {
      TherapyFeedbackBottomSheet.show(context, _currentSessionId!);
    }
  }

  Future<void> _scheduleFeedbackNotification() async {
    if (_currentSessionId != null) {
      await _feedbackService.scheduleTherapyFeedback(
        sessionId: _currentSessionId!,
        afterDuration: const Duration(seconds: 5), // 5 seconds for demo
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content:
              Text('Feedback notification scheduled for 5 seconds from now!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _simulateSessionEnd() async {
    if (_currentSessionId != null) {
      // First schedule the notification (simulating session end behavior)
      await _scheduleFeedbackNotification();

      // Also show immediate in-app feedback request after a delay
      await Future<void>.delayed(const Duration(seconds: 2));

      if (mounted) {
        await _feedbackService.showInAppFeedbackRequest(_currentSessionId!);
      }
    }
  }

  Future<void> _checkPendingFeedback() async {
    await _feedbackService.handlePendingFeedback();
  }
}
