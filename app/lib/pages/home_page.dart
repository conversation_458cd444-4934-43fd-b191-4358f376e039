import 'dart:convert';
import 'package:account_management/account_management.dart';
import 'package:account_management/application/menstrual_cycle_bloc/menstrual_cycle_bloc.dart';
import 'package:analytics/analytics.dart' hide getIt;
import 'package:auto_route/annotations.dart';
import 'package:auto_route/auto_route.dart';
import 'package:design_system/design_system.dart';
// import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:juno_plus/pages/settings/settings_page.dart';
import 'package:notifications/application/custom_notification_stream/custom_notification_stream_bloc.dart';
import 'package:notifications/application/notification_bloc/notification_bloc.dart';
import '../helpers.dart';
import '../services/therapy_feedback_service.dart';
import 'connectivity/connectivity_mapper.dart';
import 'dashboard/dashboard_page.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

@RoutePage()
class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final List<String> _tabs = ['Remote', 'Dashboard', 'Settings'];
  String _selectedTab = 'Dashboard';
  // late AppUpdateCheck _appUpdateCheck;
  // Create an instance of your controller
  // final TensController _controller = TensController();

  @override
  void dispose() {
    // Make sure to dispose the controller when the screen is disposed
    // _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // _initializeAppUpdateCheck();
    // _checkForNotifications();
  }

  // Future<void> _initializeAppUpdateCheck() async {
  //   FirebaseRemoteConfig remoteConfig = await FirebaseRemoteConfig.instance;
  //   _appUpdateCheck = AppUpdateCheck(remoteConfig);
  //   // Check for updates
  //   _appUpdateCheck.checkForUpdate(context);
  // }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (_) => getIt<NotificationBloc>()
                ..add(NotificationEvent.initialize(context)),
            ),
            BlocProvider(
              create: (_) => getIt<CustomNotificationStreamBloc>()
                ..add(CustomNotificationStreamEvent
                    .loadCustomNotificationStream()),
            ),
          ],
          child: MultiBlocListener(
            listeners: [
              BlocListener<NotificationBloc, NotificationState>(
                listener: (context, state) {
                  state.maybeMap(
                    success: (_) {
                      SnackBar(
                        content: Text('Notifications initialized successfully'),
                        duration: const Duration(seconds: 2),
                      );
                    },
                    failure: (failure) {
                      SnackBar(
                        content: Text(
                            'Failed to initialize notifications: ${failure.failure}'),
                        duration: const Duration(seconds: 2),
                      );
                    },
                    orElse: () {},
                  );
                },
              ),
              // BlocListener<BluetoothServiceBloc, BluetoothServiceState>(
              //   listener: (context, state) {
              //     state.maybeMap(
              //       bluetoothError: (error) {
              //         SnackBar(
              //           content: Text('Bluetooth Error: ${error.failure}'),
              //           duration: const Duration(seconds: 2),
              //         );
              //       },
              //       orElse: () {},
              //     );
              //   },
              // ),
              BlocListener<MenstrualCycleBloc, MenstrualCycleState>(
                listener: (context, state) {},
              ),
              // Analytics initialization listener
              BlocListener<AnalyticsInitializationBloc,
                  AnalyticsInitializationState>(
                listener: (context, state) {
                  if (state is AnalyticsInitializationLoading) {
                    // Show loading indicator for analytics initialization
                    if (kDebugMode) {
                      print('🔄 Analytics services initializing...');
                    }
                  } else if (state is AnalyticsInitializationSuccess) {
                    // Analytics initialized successfully
                    if (kDebugMode) {
                      print('✅ Analytics initialized successfully');
                      print('📊 Stats: ${state.stats}');

                      // Show detailed stats
                      final stats =
                          state.stats['storageStats'] as Map<String, dynamic>?;
                      if (stats != null) {
                        print('📱 Total sessions: ${stats['totalSessions']}');
                        print(
                            '📤 Unsynced sessions: ${stats['unsyncedSessions']}');
                        print('🌐 Online: ${stats['isOnline']}');
                        print('🔄 Syncing: ${stats['isSyncing']}');
                      }
                    }

                    // Optional: Show success snackbar in debug mode
                    if (kDebugMode) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Analytics services ready'),
                          backgroundColor: Colors.green,
                          duration: Duration(seconds: 2),
                        ),
                      );
                    }
                  } else if (state is AnalyticsInitializationFailure) {
                    // Analytics initialization failed
                    if (kDebugMode) {
                      print(
                          '❌ Analytics initialization failed: ${state.error}');
                    }

                    // Show error snackbar in debug mode
                    if (kDebugMode) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Analytics initialization failed'),
                          backgroundColor: Colors.red,
                          duration: Duration(seconds: 3),
                          action: SnackBarAction(
                            label: 'Retry',
                            textColor: Colors.white,
                            onPressed: () {
                              // Retry analytics initialization
                              context
                                  .read<AnalyticsInitializationBloc>()
                                  .add(InitializeAnalyticsEvent());
                            },
                          ),
                        ),
                      );
                    }
                  }
                },
              ),
              // Therapy management listener
              // BlocListener<TherapyManagementBloc, TherapyManagementState>(
              //   listener: (context, state) {
              //     state.maybeMap(
              //       loading: (_) {
              //         if (kDebugMode) {
              //           print('🔄 Starting therapy session...');
              //         }
              //       },
              //       therapyStarted: (_) {
              //         if (kDebugMode) {
              //           print('✅ Therapy session started successfully!');
              //           print(
              //               '📊 Session will run for 5 minutes and then auto-end');
              //         }
              //
              //         // Show success snackbar
              //         ScaffoldMessenger.of(context).showSnackBar(
              //           SnackBar(
              //             content:
              //                 Text('Therapy session started! (5 min duration)'),
              //             backgroundColor: Colors.green,
              //             duration: Duration(seconds: 3),
              //           ),
              //         );
              //       },
              //       error: (errorState) {
              //         if (kDebugMode) {
              //           print(
              //               '❌ Failed to start therapy session: ${errorState.message}');
              //         }
              //
              //         // Show error snackbar
              //         ScaffoldMessenger.of(context).showSnackBar(
              //           SnackBar(
              //             content: Text('Failed to start therapy session'),
              //             backgroundColor: Colors.red,
              //             duration: Duration(seconds: 3),
              //           ),
              //         );
              //       },
              //       orElse: () {},
              //     );
              //   },
              // ),
              // listen for custom notification stream
              BlocListener<CustomNotificationStreamBloc,
                  CustomNotificationStreamState>(
                listener: (context, state) {
                  state.maybeMap(
                    successNotification: (notification) {
                      if (notification.notification != null) {
                        print(
                            'Notification received: ${notification.notification}');
                        final notificationModel = notification.notification!;
                        print(
                            'opening notification: ${notificationModel.title}');

                        // Handle therapy feedback notifications specifically
                        if (notificationModel.payload != null) {
                          try {
                            final payloadData =
                                json.decode(notificationModel.payload!);
                            if (payloadData is Map &&
                                payloadData['type'] == 'therapy_feedback') {
                              final sessionId = payloadData['sessionId'];
                              if (sessionId != null) {
                                getIt<TherapyFeedbackService>()
                                    .handleFeedbackNotificationTap(
                                        sessionId.toString());
                                return;
                              }
                            }
                          } catch (e) {
                            // If payload is not JSON, treat as regular sessionId for backward compatibility
                            if (notificationModel.notificationType ==
                                'therapy_feedback') {
                              getIt<TherapyFeedbackService>()
                                  .handleFeedbackNotificationTap(
                                      notificationModel.payload!);
                              return;
                            }
                          }
                        }

                        // Show generic notification dialog for other types
                        showDialog<void>(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              title: Text(
                                  notificationModel.title ?? 'Notification'),
                              content:
                                  Text(notificationModel.body ?? 'No body'),
                              actions: [
                                TextButton(
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                  child: const Text('OK'),
                                ),
                              ],
                            );
                          },
                        );
                      }
                    },
                    orElse: () {},
                  );
                },
              ),
              BlocListener<PeriodReminderSettingsBloc,
                  PeriodReminderSettingsState>(
                listener: (context, state) {
                  state.maybeMap(
                    loading: (_) {
                      if (kDebugMode) {
                        print('🔄 Loading period reminder settings...');
                      }
                    },
                    loaded: (loadedState) {
                      if (kDebugMode) {
                        print('✅ Period reminder settings loaded');
                      }
                    },
                    failure: (failureState) {
                      if (kDebugMode) {
                        print(
                            '❌ Failed to load period reminder settings: ${failureState.message}');
                      }
                    },
                    orElse: () {},
                  );
                },
              ),
            ],
            child: Scaffold(
                bottomNavigationBar: Padding(
                  padding: const EdgeInsets.only(
                      left: 40.0, right: 40.0, bottom: 20.0, top: 20.0),
                  child: Container(
                    width: 1.sw,
                    height: .15.sw,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: const BorderRadius.all(Radius.circular(40)),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        GestureDetector(
                          key: const Key('remote'),
                          onTap: () {
                            setState(() {
                              _selectedTab = _tabs[0];
                            });

                            // setState(() {
                            //   context.router.push(RemoteOneRoute());
                            // });
                          },
                          child: SvgPicture.asset(
                            'assets/home/<USER>',
                            height: 30,
                            width: 30,
                            color: _selectedTab == 'Remote'
                                ? Colors.white
                                : const Color(0xffCFB4FE),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedTab = _tabs[1];
                            });
                          },
                          child: SvgPicture.asset(
                            'assets/home/<USER>',
                            height: 30,
                            width: 30,
                            color: _selectedTab == 'Dashboard'
                                ? Colors.white
                                : const Color(0xffCFB4FE),
                          ),
                        ),
                        GestureDetector(
                          key: const Key('settings'),
                          onTap: () {
                            setState(() {
                              _selectedTab = _tabs[2];
                            });
                            print('Settings clicked');

                            // DEBUG: Force resync all sessions to test sync issue
                            // if (kDebugMode) {
                            //   // _debugForceResyncSessions(context);
                            // }
                          },
                          child: SvgPicture.asset(
                            'assets/home/<USER>',
                            height: 30,
                            width: 30,
                            color: _selectedTab == 'Settings'
                                ? Colors.white
                                : const Color(0xffCFB4FE),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                body: _selectedTab == 'Dashboard'
                    ? DashboardPage()
                    : _selectedTab == 'Remote'
                        ? ConnectivityMapper()
                        : SettingsPage() // Use actual SettingsPage instead of TENS UI

                ),
          ),
        ));
  }

  /// Debug method to force resync all sessions
  // void _debugForceResyncSessions(BuildContext context) async {
  //   try {
  //     print('🔍 DEBUG: Force resyncing all sessions...');
  //
  //     // Get analytics facade from context
  //     final analyticsFacade = getIt<IAnalyticsFacade>();
  //
  //     // Force resync all sessions
  //     final result = await analyticsFacade.forceResyncAllSessions();
  //
  //     result.mapBoth(
  //       onLeft: (error) {
  //         print('❌ DEBUG: Force resync failed: $error');
  //         if (kDebugMode) {
  //           ScaffoldMessenger.of(context).showSnackBar(
  //             SnackBar(
  //               content: Text('Force resync failed: $error'),
  //               backgroundColor: Colors.red,
  //               duration: Duration(seconds: 5),
  //             ),
  //           );
  //         }
  //       },
  //       onRight: (_) {
  //         print('✅ DEBUG: Force resync completed successfully');
  //         if (kDebugMode) {
  //           ScaffoldMessenger.of(context).showSnackBar(
  //             SnackBar(
  //               content: Text('Force resync completed - check console logs'),
  //               backgroundColor: Colors.green,
  //               duration: Duration(seconds: 3),
  //             ),
  //           );
  //         }
  //       },
  //     );
  //   } catch (e) {
  //     print('❌ DEBUG: Exception during force resync: $e');
  //   }
  // }
}
